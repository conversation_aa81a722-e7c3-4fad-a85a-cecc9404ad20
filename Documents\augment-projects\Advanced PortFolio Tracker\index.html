<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Portfolio Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1 class="title">
                <span class="title-main">ADVANCED</span>
                <span class="title-accent">PORTFOLIO</span>
                <span class="title-main">TRACKER</span>
            </h1>
            <div class="upload-section">
                <input type="file" id="csvFile" accept=".csv" class="file-input">
                <label for="csvFile" class="file-label">
                    <span class="upload-icon">📊</span>
                    Upload CSV Data
                </label>
                <button id="refreshData" class="refresh-btn">
                    <span class="refresh-icon">🔄</span>
                    Refresh Prices
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Panel - Charts -->
            <section class="chart-panel">
                <div class="panel-header">
                    <h2>Portfolio Analytics</h2>
                    <div class="chart-controls">
                        <button id="pieChartBtn" class="chart-btn active">Allocation</button>
                        <button id="lineChartBtn" class="chart-btn">Performance</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="portfolioChart"></canvas>
                </div>
                <div class="portfolio-summary">
                    <div class="summary-item">
                        <span class="summary-label">Total Value</span>
                        <span class="summary-value" id="totalValue">$0.00</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Gain/Loss</span>
                        <span class="summary-value" id="totalGainLoss">$0.00</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Return</span>
                        <span class="summary-value" id="totalReturn">0.00%</span>
                    </div>
                </div>
            </section>

            <!-- Right Panel - Holdings Table -->
            <section class="holdings-panel">
                <div class="panel-header">
                    <h2>Portfolio Holdings</h2>
                    <div class="holdings-controls">
                        <span class="holdings-count" id="holdingsCount">0 positions</span>
                        <button id="exportBtn" class="export-btn">Export Data</button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="holdings-table" id="holdingsTable">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Shares</th>
                                <th>Avg Cost</th>
                                <th>Current Price</th>
                                <th>Market Value</th>
                                <th>Gain/Loss</th>
                                <th>Return %</th>
                                <th>Allocation</th>
                            </tr>
                        </thead>
                        <tbody id="holdingsBody">
                            <!-- Holdings data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-item">
                <span class="status-label">Last Updated:</span>
                <span class="status-value" id="lastUpdated">Never</span>
            </div>
            <div class="status-item">
                <span class="status-label">Data Source:</span>
                <span class="status-value" id="dataSource">No file loaded</span>
            </div>
            <div class="status-item">
                <span class="status-label">Market Status:</span>
                <span class="status-value" id="marketStatus">Closed</span>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing portfolio data...</div>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Error</h3>
                <button class="modal-close" id="closeErrorModal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="errorMessage">An error occurred while processing your request.</p>
            </div>
        </div>
    </div>

    <script src="portfolio.js"></script>
</body>
</html>

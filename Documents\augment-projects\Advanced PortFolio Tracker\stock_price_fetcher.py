#!/usr/bin/env python3
"""
Advanced Portfolio Tracker - Stock Price Fetcher
Fetches real-time stock prices for ASX stocks using yfinance
"""

import yfinance as yf
import json
import sys
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ASXStockPriceFetcher:
    """Fetches real-time stock prices for ASX (Australian Securities Exchange) stocks"""
    
    def __init__(self):
        self.session = None
        
    def convert_asx_code(self, csv_code: str) -> str:
        """
        Convert ASX stock code from CSV format to yfinance format
        Example: 'DRO.ASX' -> 'DRO.AX'
        """
        if '.ASX' in csv_code.upper():
            return csv_code.upper().replace('.ASX', '.AX')
        elif '.' not in csv_code:
            # If no suffix, assume it's ASX and add .AX
            return f"{csv_code.upper()}.AX"
        else:
            # Already in correct format or different exchange
            return csv_code.upper()
    
    def fetch_stock_price(self, symbol: str) -> Optional[Dict]:
        """
        Fetch current stock price and basic info for a single symbol
        """
        try:
            ticker = yf.Ticker(symbol)
            
            # Get current price and basic info
            info = ticker.info
            hist = ticker.history(period="1d")
            
            if hist.empty:
                logger.warning(f"No price data available for {symbol}")
                return None
            
            current_price = hist['Close'].iloc[-1]
            previous_close = info.get('previousClose', current_price)
            
            # Calculate daily change
            daily_change = current_price - previous_close
            daily_change_percent = (daily_change / previous_close) * 100 if previous_close > 0 else 0
            
            return {
                'symbol': symbol,
                'current_price': round(float(current_price), 2),
                'previous_close': round(float(previous_close), 2),
                'daily_change': round(float(daily_change), 2),
                'daily_change_percent': round(float(daily_change_percent), 2),
                'volume': int(hist['Volume'].iloc[-1]) if not hist['Volume'].empty else 0,
                'market_cap': info.get('marketCap'),
                'currency': info.get('currency', 'AUD'),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return None
    
    def fetch_multiple_stocks(self, symbols: List[str]) -> Dict:
        """
        Fetch stock prices for multiple symbols
        """
        results = {
            'success': True,
            'prices': {},
            'errors': [],
            'timestamp': datetime.now().isoformat(),
            'market_status': self.get_market_status()
        }
        
        logger.info(f"Fetching prices for {len(symbols)} symbols: {symbols}")
        
        for symbol in symbols:
            try:
                # Convert to yfinance format
                yf_symbol = self.convert_asx_code(symbol)
                logger.info(f"Fetching data for {symbol} -> {yf_symbol}")
                
                stock_data = self.fetch_stock_price(yf_symbol)
                
                if stock_data:
                    results['prices'][yf_symbol] = stock_data['current_price']
                    logger.info(f"Successfully fetched {yf_symbol}: ${stock_data['current_price']}")
                else:
                    error_msg = f"Failed to fetch data for {yf_symbol}"
                    results['errors'].append(error_msg)
                    logger.warning(error_msg)
                    
            except Exception as e:
                error_msg = f"Error processing {symbol}: {str(e)}"
                results['errors'].append(error_msg)
                logger.error(error_msg)
        
        # If no prices were fetched successfully, mark as failed
        if not results['prices']:
            results['success'] = False
            results['error'] = 'No stock prices could be fetched'
        
        return results
    
    def get_market_status(self) -> str:
        """
        Determine if ASX market is currently open
        ASX trading hours: 10:00 AM - 4:00 PM AEST (Monday to Friday)
        """
        try:
            from pytz import timezone
            import pytz
            
            # Get current time in Australian Eastern Time
            aest = timezone('Australia/Sydney')
            now = datetime.now(aest)
            
            # Check if it's a weekday
            if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return 'Closed (Weekend)'
            
            # Check if it's within trading hours (10:00 AM - 4:00 PM)
            market_open = now.replace(hour=10, minute=0, second=0, microsecond=0)
            market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
            
            if market_open <= now <= market_close:
                return 'Open'
            else:
                return 'Closed'
                
        except ImportError:
            # If pytz is not available, return unknown
            return 'Unknown'
        except Exception as e:
            logger.warning(f"Error determining market status: {str(e)}")
            return 'Unknown'
    
    def save_to_file(self, data: Dict, filename: str = 'stock_prices.json'):
        """Save fetched data to JSON file"""
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Data saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving data to file: {str(e)}")

def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description='Fetch ASX stock prices')
    parser.add_argument('symbols', nargs='+', help='Stock symbols to fetch (e.g., DRO.ASX CBA.ASX)')
    parser.add_argument('--output', '-o', help='Output JSON file', default='stock_prices.json')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize fetcher
    fetcher = ASXStockPriceFetcher()
    
    # Fetch stock prices
    results = fetcher.fetch_multiple_stocks(args.symbols)
    
    # Save to file
    fetcher.save_to_file(results, args.output)
    
    # Print results
    print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if results['success'] else 1)

def fetch_prices_for_web(symbols_json: str) -> str:
    """
    Function to be called from web application
    Takes JSON string of symbols, returns JSON string of results
    """
    try:
        symbols = json.loads(symbols_json)
        fetcher = ASXStockPriceFetcher()
        results = fetcher.fetch_multiple_stocks(symbols)
        return json.dumps(results)
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }
        return json.dumps(error_result)

if __name__ == '__main__':
    main()

// Advanced Portfolio Tracker - Main JavaScript File

class PortfolioTracker {
    constructor() {
        this.transactions = [];
        this.holdings = new Map();
        this.currentPrices = new Map();
        this.chart = null;
        this.chartType = 'pie';
        
        this.initializeEventListeners();
        this.updateLastUpdated();
    }

    initializeEventListeners() {
        // File upload
        document.getElementById('csvFile').addEventListener('change', (e) => {
            this.handleFileUpload(e);
        });

        // Chart type toggle
        document.getElementById('pieChartBtn').addEventListener('click', () => {
            this.switchChart('pie');
        });
        
        document.getElementById('lineChartBtn').addEventListener('click', () => {
            this.switchChart('line');
        });

        // Refresh data
        document.getElementById('refreshData').addEventListener('click', () => {
            this.refreshPrices();
        });

        // Export data
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportData();
        });

        // Close error modal
        document.getElementById('closeErrorModal').addEventListener('click', () => {
            this.hideModal('errorModal');
        });
    }

    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        this.showLoading();
        
        Papa.parse(file, {
            header: true,
            skipEmptyLines: true,
            complete: (results) => {
                try {
                    this.processCSVData(results.data);
                    this.updateDataSource(file.name);
                    this.hideLoading();
                } catch (error) {
                    this.hideLoading();
                    this.showError('Error processing CSV file: ' + error.message);
                }
            },
            error: (error) => {
                this.hideLoading();
                this.showError('Error reading CSV file: ' + error.message);
            }
        });
    }

    processCSVData(data) {
        this.transactions = [];
        this.holdings.clear();

        // Validate CSV format
        const requiredColumns = ['Date', 'Confirmation No.', 'Code', 'Quantity', 'Action', 'Avg. price', 'Fees', 'Settl. value'];
        const headers = Object.keys(data[0] || {});
        
        for (const col of requiredColumns) {
            if (!headers.includes(col)) {
                throw new Error(`Missing required column: ${col}`);
            }
        }

        // Process each transaction
        data.forEach((row, index) => {
            try {
                const transaction = this.parseTransaction(row);
                this.transactions.push(transaction);
                this.updateHoldings(transaction);
            } catch (error) {
                console.warn(`Error processing row ${index + 1}:`, error.message);
            }
        });

        this.calculatePortfolioMetrics();
        this.updateDisplay();
    }

    parseTransaction(row) {
        const transaction = {
            date: new Date(row['Date']),
            confirmationNo: row['Confirmation No.'],
            code: row['Code'].trim().toUpperCase(),
            quantity: parseFloat(row['Quantity']) || 0,
            action: row['Action'].toLowerCase().trim(),
            avgPrice: parseFloat(row['Avg. price']) || 0,
            fees: parseFloat(row['Fees']) || 0,
            settlValue: parseFloat(row['Settl. value']) || 0
        };

        // Validate transaction data
        if (!transaction.code || transaction.quantity <= 0 || transaction.avgPrice <= 0) {
            throw new Error('Invalid transaction data');
        }

        if (!['buy', 'sell'].includes(transaction.action)) {
            throw new Error(`Invalid action: ${transaction.action}`);
        }

        return transaction;
    }

    updateHoldings(transaction) {
        const { code, quantity, action, avgPrice, fees } = transaction;
        
        if (!this.holdings.has(code)) {
            this.holdings.set(code, {
                symbol: code,
                totalShares: 0,
                totalCost: 0,
                totalFees: 0,
                avgCostPerShare: 0,
                transactions: []
            });
        }

        const holding = this.holdings.get(code);
        holding.transactions.push(transaction);

        if (action === 'buy') {
            const totalCostWithFees = (quantity * avgPrice) + fees;
            holding.totalCost += totalCostWithFees;
            holding.totalShares += quantity;
            holding.totalFees += fees;
        } else if (action === 'sell') {
            const sellValue = quantity * avgPrice;
            const proportionalCost = (quantity / holding.totalShares) * holding.totalCost;
            
            holding.totalShares -= quantity;
            holding.totalCost -= proportionalCost;
            holding.totalFees += fees;
        }

        // Calculate average cost per share
        if (holding.totalShares > 0) {
            holding.avgCostPerShare = holding.totalCost / holding.totalShares;
        } else {
            holding.avgCostPerShare = 0;
        }

        // Remove holdings with zero shares
        if (holding.totalShares <= 0) {
            this.holdings.delete(code);
        }
    }

    async refreshPrices() {
        if (this.holdings.size === 0) {
            this.showError('No holdings to refresh. Please upload a CSV file first.');
            return;
        }

        this.showLoading();
        
        try {
            // Convert ASX codes to yfinance format
            const symbols = Array.from(this.holdings.keys()).map(code => {
                // Convert DRO.ASX to DRO.AX format
                return code.replace('.ASX', '.AX');
            });

            // Call Python script to fetch prices
            const response = await this.fetchStockPrices(symbols);
            
            if (response.success) {
                this.currentPrices.clear();
                
                // Map prices back to original codes
                Object.entries(response.prices).forEach(([symbol, price]) => {
                    const originalCode = symbol.replace('.AX', '.ASX');
                    this.currentPrices.set(originalCode, price);
                });

                this.calculatePortfolioMetrics();
                this.updateDisplay();
                this.updateLastUpdated();
                this.updateMarketStatus('Open');
            } else {
                throw new Error(response.error || 'Failed to fetch stock prices');
            }
        } catch (error) {
            this.showError('Error refreshing prices: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async fetchStockPrices(symbols) {
        try {
            // This would normally call the Python script
            // For now, we'll simulate with mock data
            const mockPrices = {};
            symbols.forEach(symbol => {
                // Generate mock prices between $10-$100
                mockPrices[symbol] = (Math.random() * 90 + 10).toFixed(2);
            });

            return {
                success: true,
                prices: mockPrices
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    calculatePortfolioMetrics() {
        let totalValue = 0;
        let totalCost = 0;

        this.holdings.forEach((holding, code) => {
            const currentPrice = this.currentPrices.get(code) || holding.avgCostPerShare;
            const marketValue = holding.totalShares * currentPrice;
            const gainLoss = marketValue - holding.totalCost;
            const returnPercent = holding.totalCost > 0 ? (gainLoss / holding.totalCost) * 100 : 0;

            // Update holding with calculated values
            holding.currentPrice = currentPrice;
            holding.marketValue = marketValue;
            holding.gainLoss = gainLoss;
            holding.returnPercent = returnPercent;

            totalValue += marketValue;
            totalCost += holding.totalCost;
        });

        this.portfolioMetrics = {
            totalValue,
            totalCost,
            totalGainLoss: totalValue - totalCost,
            totalReturn: totalCost > 0 ? ((totalValue - totalCost) / totalCost) * 100 : 0
        };
    }

    updateDisplay() {
        this.updateSummary();
        this.updateHoldingsTable();
        this.updateChart();
    }

    updateSummary() {
        if (!this.portfolioMetrics) return;

        const { totalValue, totalGainLoss, totalReturn } = this.portfolioMetrics;

        document.getElementById('totalValue').textContent = this.formatCurrency(totalValue);
        
        const gainLossElement = document.getElementById('totalGainLoss');
        gainLossElement.textContent = this.formatCurrency(totalGainLoss);
        gainLossElement.className = `summary-value ${totalGainLoss >= 0 ? 'positive' : 'negative'}`;
        
        const returnElement = document.getElementById('totalReturn');
        returnElement.textContent = this.formatPercent(totalReturn);
        returnElement.className = `summary-value ${totalReturn >= 0 ? 'positive' : 'negative'}`;
    }

    updateHoldingsTable() {
        const tbody = document.getElementById('holdingsBody');
        tbody.innerHTML = '';

        if (this.holdings.size === 0) {
            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: var(--text-muted);">No holdings data available</td></tr>';
            document.getElementById('holdingsCount').textContent = '0 positions';
            return;
        }

        const sortedHoldings = Array.from(this.holdings.entries())
            .sort((a, b) => b[1].marketValue - a[1].marketValue);

        sortedHoldings.forEach(([code, holding]) => {
            const row = document.createElement('tr');
            
            const allocation = this.portfolioMetrics.totalValue > 0 
                ? (holding.marketValue / this.portfolioMetrics.totalValue) * 100 
                : 0;

            row.innerHTML = `
                <td><strong>${holding.symbol}</strong></td>
                <td>${this.formatNumber(holding.totalShares)}</td>
                <td>${this.formatCurrency(holding.avgCostPerShare)}</td>
                <td>${this.formatCurrency(holding.currentPrice)}</td>
                <td>${this.formatCurrency(holding.marketValue)}</td>
                <td class="${holding.gainLoss >= 0 ? 'positive' : 'negative'}">${this.formatCurrency(holding.gainLoss)}</td>
                <td class="${holding.returnPercent >= 0 ? 'positive' : 'negative'}">${this.formatPercent(holding.returnPercent)}</td>
                <td>${this.formatPercent(allocation)}</td>
            `;
            
            tbody.appendChild(row);
        });

        document.getElementById('holdingsCount').textContent = `${this.holdings.size} position${this.holdings.size !== 1 ? 's' : ''}`;
    }

    switchChart(type) {
        this.chartType = type;
        
        // Update button states
        document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`${type}ChartBtn`).classList.add('active');
        
        this.updateChart();
    }

    updateChart() {
        if (this.chart) {
            this.chart.destroy();
        }

        const ctx = document.getElementById('portfolioChart').getContext('2d');
        
        if (this.chartType === 'pie') {
            this.createPieChart(ctx);
        } else {
            this.createLineChart(ctx);
        }
    }

    createPieChart(ctx) {
        if (this.holdings.size === 0) return;

        const data = Array.from(this.holdings.entries()).map(([code, holding]) => ({
            label: holding.symbol,
            value: holding.marketValue,
            percentage: this.portfolioMetrics.totalValue > 0 
                ? (holding.marketValue / this.portfolioMetrics.totalValue) * 100 
                : 0
        }));

        const colors = this.generateColors(data.length);

        this.chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.label),
                datasets: [{
                    data: data.map(item => item.value),
                    backgroundColor: colors,
                    borderColor: '#00ffff',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            font: {
                                family: 'Courier New',
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: '#111111',
                        titleColor: '#00ffff',
                        bodyColor: '#ffffff',
                        borderColor: '#00ffff',
                        borderWidth: 1,
                        callbacks: {
                            label: (context) => {
                                const item = data[context.dataIndex];
                                return `${item.label}: ${this.formatCurrency(item.value)} (${this.formatPercent(item.percentage)})`;
                            }
                        }
                    }
                }
            }
        });
    }

    createLineChart(ctx) {
        // For now, create a simple mock performance chart
        // In a real implementation, this would show portfolio value over time
        const mockData = this.generateMockPerformanceData();

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: mockData.labels,
                datasets: [{
                    label: 'Portfolio Value',
                    data: mockData.values,
                    borderColor: '#00ffff',
                    backgroundColor: 'rgba(0, 255, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        ticks: {
                            color: '#ffffff',
                            font: {
                                family: 'Courier New'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 255, 255, 0.2)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#ffffff',
                            font: {
                                family: 'Courier New'
                            },
                            callback: (value) => this.formatCurrency(value)
                        },
                        grid: {
                            color: 'rgba(0, 255, 255, 0.2)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff',
                            font: {
                                family: 'Courier New'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: '#111111',
                        titleColor: '#00ffff',
                        bodyColor: '#ffffff',
                        borderColor: '#00ffff',
                        borderWidth: 1
                    }
                }
            }
        });
    }

    generateMockPerformanceData() {
        const labels = [];
        const values = [];
        const baseValue = this.portfolioMetrics?.totalValue || 50000;
        
        // Generate 30 days of mock data
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString());
            
            // Generate realistic portfolio fluctuation
            const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
            const value = baseValue * (1 + variation * (i / 30));
            values.push(value);
        }
        
        return { labels, values };
    }

    generateColors(count) {
        const colors = [];
        const hueStep = 360 / count;
        
        for (let i = 0; i < count; i++) {
            const hue = i * hueStep;
            colors.push(`hsl(${hue}, 70%, 50%)`);
        }
        
        return colors;
    }

    exportData() {
        if (this.holdings.size === 0) {
            this.showError('No data to export. Please upload a CSV file first.');
            return;
        }

        const exportData = {
            summary: this.portfolioMetrics,
            holdings: Array.from(this.holdings.entries()).map(([code, holding]) => ({
                symbol: holding.symbol,
                shares: holding.totalShares,
                avgCost: holding.avgCostPerShare,
                currentPrice: holding.currentPrice,
                marketValue: holding.marketValue,
                gainLoss: holding.gainLoss,
                returnPercent: holding.returnPercent
            })),
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `portfolio-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Utility methods
    formatCurrency(value) {
        return new Intl.NumberFormat('en-AU', {
            style: 'currency',
            currency: 'AUD'
        }).format(value || 0);
    }

    formatPercent(value) {
        return `${(value || 0).toFixed(2)}%`;
    }

    formatNumber(value) {
        return new Intl.NumberFormat('en-AU').format(value || 0);
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.add('show');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorModal').classList.add('show');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }

    updateLastUpdated() {
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
    }

    updateDataSource(filename) {
        document.getElementById('dataSource').textContent = filename;
    }

    updateMarketStatus(status) {
        document.getElementById('marketStatus').textContent = status;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PortfolioTracker();
});

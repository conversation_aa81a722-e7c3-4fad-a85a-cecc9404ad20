/* Advanced Portfolio Tracker - Noir/Cyberpunk Theme */

:root {
    --primary-bg: #000000;
    --secondary-bg: #0a0a0a;
    --panel-bg: #111111;
    --border-color: #00ffff;
    --accent-color: #00ffff;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --success-color: #00ff88;
    --danger-color: #ff0066;
    --warning-color: #ffaa00;
    --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.3);
    --shadow-subtle: 0 2px 10px rgba(0, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: var(--secondary-bg);
    border-bottom: 2px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-glow);
}

.title {
    font-size: 2rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.title-main {
    color: var(--text-primary);
}

.title-accent {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
}

.upload-section {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.file-input {
    display: none;
}

.file-label {
    background: var(--panel-bg);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 1px;
}

.file-label:hover {
    background: var(--accent-color);
    color: var(--primary-bg);
    box-shadow: var(--shadow-glow);
}

.refresh-btn {
    background: var(--panel-bg);
    border: 2px solid var(--success-color);
    color: var(--success-color);
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 1px;
}

.refresh-btn:hover {
    background: var(--success-color);
    color: var(--primary-bg);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

/* Panel Styles */
.chart-panel,
.holdings-panel {
    background: var(--panel-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--shadow-subtle);
    display: flex;
    flex-direction: column;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.panel-header h2 {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 1.2rem;
}

/* Chart Controls */
.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
}

.chart-btn.active,
.chart-btn:hover {
    background: var(--accent-color);
    color: var(--primary-bg);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Chart Container */
.chart-container {
    flex: 1;
    position: relative;
    min-height: 300px;
    margin-bottom: 1.5rem;
}

#portfolioChart {
    max-width: 100%;
    max-height: 100%;
}

/* Portfolio Summary */
.portfolio-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.summary-item {
    text-align: center;
    padding: 1rem;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.summary-label {
    display: block;
    color: var(--text-muted);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.summary-value {
    display: block;
    color: var(--accent-color);
    font-size: 1.2rem;
    font-weight: bold;
    text-shadow: 0 0 5px var(--accent-color);
}

/* Holdings Controls */
.holdings-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.holdings-count {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.export-btn {
    background: transparent;
    border: 1px solid var(--warning-color);
    color: var(--warning-color);
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
}

.export-btn:hover {
    background: var(--warning-color);
    color: var(--primary-bg);
    box-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
}

/* Table Styles */
.table-container {
    flex: 1;
    overflow-x: auto;
    overflow-y: auto;
    max-height: 500px;
}

.holdings-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.holdings-table th {
    background: var(--secondary-bg);
    color: var(--accent-color);
    padding: 1rem 0.75rem;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 1px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.holdings-table td {
    padding: 0.75rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
    transition: background-color 0.3s ease;
}

.holdings-table tbody tr:hover {
    background: rgba(0, 255, 255, 0.05);
}

.positive {
    color: var(--success-color);
}

.negative {
    color: var(--danger-color);
}

/* Status Bar */
.status-bar {
    background: var(--secondary-bg);
    border-top: 2px solid var(--border-color);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.status-item {
    display: flex;
    gap: 0.5rem;
}

.status-label {
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-value {
    color: var(--accent-color);
    font-weight: bold;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--panel-bg);
    border: 2px solid var(--danger-color);
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(255, 0, 102, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--danger-color);
}

.modal-header h3 {
    color: var(--danger-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-close {
    background: none;
    border: none;
    color: var(--danger-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 1.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .portfolio-summary {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .upload-section {
        flex-direction: column;
        width: 100%;
    }
    
    .file-label,
    .refresh-btn {
        width: 100%;
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .holdings-table {
        font-size: 0.8rem;
    }
    
    .holdings-table th,
    .holdings-table td {
        padding: 0.5rem 0.25rem;
    }
}
